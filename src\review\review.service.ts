import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Review, ReviewDocument } from './review.model';
import { CreateReviewDto } from './dto/create-review.dto';
import { ProductService } from '../product/product.service';
import { FileService } from '../file/file.service';

@Injectable()
export class ReviewService {
  constructor(
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
    private productService: ProductService,
    private fileService: FileService,
  ) {}

  async create(createReviewDto: CreateReviewDto, userId: string, userName: string): Promise<Review> {
    // Verificar que el producto existe
    const product = await this.productService.findOne(createReviewDto.productId);
    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Verificar que el usuario no haya dejado ya una reseña para este producto
    const existingReview = await this.reviewModel.findOne({
      productId: createReviewDto.productId,
      userId,
    });

    if (existingReview) {
      throw new ForbiddenException('You have already reviewed this product');
    }

    const review = new this.reviewModel({
      ...createReviewDto,
      productId: new Types.ObjectId(createReviewDto.productId),
      userId,
      userName,
    });

    const savedReview = await review.save();

    // Actualizar rating del producto
    await this.updateProductRating(createReviewDto.productId);

    return savedReview;
  }

  async findByProduct(productId: string, page: number = 1, limit: number = 10): Promise<{
    reviews: Review[];
    total: number;
    page: number;
    totalPages: number;
    averageRating: number;
    ratingDistribution: { [key: number]: number };
  }> {
    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      this.reviewModel
        .find({ productId: new Types.ObjectId(productId) })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.reviewModel.countDocuments({ productId: new Types.ObjectId(productId) }),
    ]);

    // Calcular estadísticas de rating
    const ratingStats = await this.reviewModel.aggregate([
      { $match: { productId: new Types.ObjectId(productId) } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          ratings: { $push: '$rating' },
        },
      },
    ]);

    let averageRating = 0;
    let ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    if (ratingStats.length > 0) {
      averageRating = Math.round(ratingStats[0].averageRating * 10) / 10;
      
      // Calcular distribución de ratings
      ratingStats[0].ratings.forEach((rating: number) => {
        ratingDistribution[rating]++;
      });
    }

    return {
      reviews,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      averageRating,
      ratingDistribution,
    };
  }

  async findByUser(userId: string, page: number = 1, limit: number = 10): Promise<{
    reviews: Review[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      this.reviewModel
        .find({ userId })
        .populate('productId')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.reviewModel.countDocuments({ userId }),
    ]);

    return {
      reviews,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async uploadReviewImages(reviewId: string, files: Express.Multer.File[], userId: string): Promise<Review> {
    const review = await this.reviewModel.findById(reviewId);

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    if (review.userId !== userId) {
      throw new ForbiddenException('You can only upload images to your own reviews');
    }

    const imageUrls = await this.fileService.uploadReviewImages(files);
    review.images = review.images || [];
    review.images.push(...imageUrls);
    
    return review.save();
  }

  async markHelpful(reviewId: string): Promise<Review> {
    const review = await this.reviewModel.findByIdAndUpdate(
      reviewId,
      { $inc: { helpfulCount: 1 } },
      { new: true }
    );

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  async remove(reviewId: string, userId: string): Promise<void> {
    const review = await this.reviewModel.findById(reviewId);

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    if (review.userId !== userId) {
      throw new ForbiddenException('You can only delete your own reviews');
    }

    await this.reviewModel.findByIdAndDelete(reviewId);

    // Actualizar rating del producto
    await this.updateProductRating(review.productId.toString());
  }

  private async updateProductRating(productId: string): Promise<void> {
    const ratingStats = await this.reviewModel.aggregate([
      { $match: { productId: new Types.ObjectId(productId) } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          count: { $sum: 1 },
        },
      },
    ]);

    if (ratingStats.length > 0) {
      const newRating = Math.round(ratingStats[0].averageRating * 10) / 10;
      await this.productService.updateRating(productId, newRating);
    } else {
      await this.productService.updateRating(productId, 0);
    }
  }
}
