import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AuthClientService } from '../auth-client/auth-client.service';
import { StoreService } from './store.service';

@Injectable()
export class StoreSyncService {
  constructor(
    private readonly authClient: AuthClientService,
    private readonly storeService: StoreService,
  ) {}

  @Cron(CronExpression.EVERY_6_HOURS)
  async syncStores() {
    try {
      console.log('🔄 Starting store synchronization...');
      
      const activeStores = await this.authClient.getActiveStores();
      
      for (const store of activeStores) {
        try {
          await this.storeService.updateStoreCache(store);
          console.log(`✅ Synced store: ${store.storeName}`);
        } catch (error) {
          console.error(`❌ Error syncing store ${store.storeName}:`, error);
        }
      }
      
      console.log(`🎉 Store synchronization completed. Synced ${activeStores.length} stores.`);
    } catch (error) {
      console.error('❌ Error during store synchronization:', error);
    }
  }

  async syncSingleStore(storeId: string): Promise<void> {
    try {
      const storeInfo = await this.authClient.getStoreInfo(storeId);
      
      if (storeInfo) {
        await this.storeService.updateStoreCache(storeInfo);
        console.log(`✅ Manually synced store: ${storeInfo.storeName}`);
      } else {
        console.log(`⚠️ Store not found in auth service: ${storeId}`);
      }
    } catch (error) {
      console.error(`❌ Error syncing single store ${storeId}:`, error);
      throw error;
    }
  }

  async forceSyncAllStores(): Promise<void> {
    console.log('🔄 Force syncing all stores...');
    await this.syncStores();
  }
}
