import { <PERSON>, Get, Param, Res, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import { FileService } from './file.service';
import * as fs from 'fs';
import * as path from 'path';

@Controller('files')
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Get('products/:filename')
  async getProductImage(@Param('filename') filename: string, @Res() res: Response) {
    const filePath = this.fileService.getFilePath('products', filename);
    
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('File not found');
    }

    res.sendFile(path.resolve(filePath));
  }

  @Get('stores/:filename')
  async getStoreImage(@Param('filename') filename: string, @Res() res: Response) {
    const filePath = this.fileService.getFilePath('stores', filename);
    
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('File not found');
    }

    res.sendFile(path.resolve(filePath));
  }

  @Get('reviews/:filename')
  async getReviewImage(@Param('filename') filename: string, @Res() res: Response) {
    const filePath = this.fileService.getFilePath('reviews', filename);
    
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('File not found');
    }

    res.sendFile(path.resolve(filePath));
  }
}
