import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';
import { CatalogAuthGuard } from '../auth-client/guards/catalog-auth.guard';
import { StoreOwnerGuard } from '../auth-client/guards/store-owner.guard';
import { GetUser } from '../auth-client/decorators/get-user.decorator';
import { AuthUser } from '../auth-client/auth-client.service';

@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  async create(
    @Body() createProductDto: CreateProductDto,
    @GetUser() user: AuthUser,
  ) {
    return this.productService.create(createProductDto, user.storeId);
  }

  @Get()
  async findAll(@Query() filters: ProductFiltersDto) {
    return this.productService.findAll(filters);
  }

  @Get('store/:storeId')
  async findByStore(
    @Param('storeId') storeId: string,
    @Query() filters: ProductFiltersDto,
  ) {
    return this.productService.findByStore(storeId, filters);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.productService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @GetUser() user: AuthUser,
  ) {
    return this.productService.update(id, updateProductDto, user.storeId);
  }

  @Delete(':id')
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  async remove(
    @Param('id') id: string,
    @GetUser() user: AuthUser,
  ) {
    return this.productService.remove(id, user.storeId);
  }

  @Post(':id/images')
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  @UseInterceptors(FilesInterceptor('images', 5))
  async uploadImages(
    @Param('id') id: string,
    @UploadedFiles() files: Express.Multer.File[],
    @GetUser() user: AuthUser,
  ) {
    return this.productService.uploadImages(id, files, user.storeId);
  }
}
