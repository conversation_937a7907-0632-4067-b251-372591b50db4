import { Injectable, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product, ProductDocument } from './product.model';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';
import { FileService } from '../file/file.service';

@Injectable()
export class ProductService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    private fileService: FileService,
  ) {}

  async create(createProductDto: CreateProductDto, storeId: string): Promise<Product> {
    const product = new this.productModel({
      ...createProductDto,
      storeId,
    });
    return product.save();
  }

  async findAll(filters: ProductFiltersDto): Promise<{ products: Product[]; total: number; page: number; totalPages: number }> {
    const query: any = { isActive: true };

    // Aplicar filtros
    if (filters.category) {
      query.category = new Types.ObjectId(filters.category);
    }

    if (filters.subcategory) {
      query.subcategory = filters.subcategory;
    }

    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      query.price = {};
      if (filters.minPrice !== undefined) query.price.$gte = filters.minPrice;
      if (filters.maxPrice !== undefined) query.price.$lte = filters.maxPrice;
    }

    if (filters.tags && filters.tags.length > 0) {
      query.tags = { $in: filters.tags };
    }

    if (filters.isAvailable !== undefined) {
      query.isAvailable = filters.isAvailable;
    }

    if (filters.minRating !== undefined) {
      query.rating = { $gte: filters.minRating };
    }

    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    // Configurar ordenamiento
    const sort: any = {};
    if (filters.sortBy) {
      sort[filters.sortBy] = filters.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1;
    }

    // Paginación
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .populate('category')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    return {
      products,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findByStore(storeId: string, filters: ProductFiltersDto): Promise<{ products: Product[]; total: number; page: number; totalPages: number }> {
    const storeFilters = { ...filters };
    const query: any = { storeId, isActive: true };

    // Aplicar los mismos filtros que en findAll pero con storeId
    if (storeFilters.category) {
      query.category = new Types.ObjectId(storeFilters.category);
    }

    if (storeFilters.subcategory) {
      query.subcategory = storeFilters.subcategory;
    }

    if (storeFilters.minPrice !== undefined || storeFilters.maxPrice !== undefined) {
      query.price = {};
      if (storeFilters.minPrice !== undefined) query.price.$gte = storeFilters.minPrice;
      if (storeFilters.maxPrice !== undefined) query.price.$lte = storeFilters.maxPrice;
    }

    if (storeFilters.tags && storeFilters.tags.length > 0) {
      query.tags = { $in: storeFilters.tags };
    }

    if (storeFilters.isAvailable !== undefined) {
      query.isAvailable = storeFilters.isAvailable;
    }

    if (storeFilters.minRating !== undefined) {
      query.rating = { $gte: storeFilters.minRating };
    }

    if (storeFilters.search) {
      query.$text = { $search: storeFilters.search };
    }

    const sort: any = {};
    if (storeFilters.sortBy) {
      sort[storeFilters.sortBy] = storeFilters.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1;
    }

    const page = storeFilters.page || 1;
    const limit = storeFilters.limit || 10;
    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .populate('category')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    return {
      products,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productModel
      .findById(id)
      .populate('category')
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, storeId: string): Promise<Product> {
    const product = await this.productModel.findById(id);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    if (product.storeId !== storeId) {
      throw new ForbiddenException('You can only update your own products');
    }

    Object.assign(product, updateProductDto);
    return product.save();
  }

  async remove(id: string, storeId: string): Promise<void> {
    const product = await this.productModel.findById(id);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    if (product.storeId !== storeId) {
      throw new ForbiddenException('You can only delete your own products');
    }

    await this.productModel.findByIdAndDelete(id);
  }

  async uploadImages(productId: string, files: Express.Multer.File[], storeId: string): Promise<Product> {
    const product = await this.productModel.findById(productId);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    if (product.storeId !== storeId) {
      throw new ForbiddenException('You can only upload images to your own products');
    }

    const imageUrls = await this.fileService.uploadProductImages(files);
    product.images.push(...imageUrls);
    
    return product.save();
  }

  async updateRating(productId: string, newRating: number): Promise<void> {
    const product = await this.productModel.findById(productId);
    if (product) {
      product.rating = newRating;
      await product.save();
    }
  }
}
