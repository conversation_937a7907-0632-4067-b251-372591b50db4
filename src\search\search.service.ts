import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product, ProductDocument } from '../product/product.model';
import { StoreCache, StoreCacheDocument } from '../store/store-cache.model';
import { SearchDto, CategoryFiltersDto } from './dto/search.dto';

@Injectable()
export class SearchService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(StoreCache.name) private storeCacheModel: Model<StoreCacheDocument>,
  ) {}

  async globalSearch(searchDto: SearchDto): Promise<{
    products: Product[];
    stores: StoreCache[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const query: any = { isActive: true, isAvailable: true };

    // Búsqueda por texto
    if (searchDto.q) {
      query.$text = { $search: searchDto.q };
    }

    // Filtros adicionales
    if (searchDto.storeId) {
      query.storeId = searchDto.storeId;
    }

    if (searchDto.category) {
      query.category = new Types.ObjectId(searchDto.category);
    }

    if (searchDto.minPrice !== undefined || searchDto.maxPrice !== undefined) {
      query.price = {};
      if (searchDto.minPrice !== undefined) query.price.$gte = searchDto.minPrice;
      if (searchDto.maxPrice !== undefined) query.price.$lte = searchDto.maxPrice;
    }

    if (searchDto.tags && searchDto.tags.length > 0) {
      query.tags = { $in: searchDto.tags };
    }

    if (searchDto.minRating !== undefined) {
      query.rating = { $gte: searchDto.minRating };
    }

    // Configurar ordenamiento
    const sort: any = {};
    if (searchDto.sortBy === 'relevance' && searchDto.q) {
      sort.score = { $meta: 'textScore' };
    } else if (searchDto.sortBy) {
      sort[searchDto.sortBy] = searchDto.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1;
    }

    // Paginación
    const page = searchDto.page || 1;
    const limit = searchDto.limit || 10;
    const skip = (page - 1) * limit;

    // Buscar productos
    const [products, totalProducts] = await Promise.all([
      this.productModel
        .find(query)
        .populate('category')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    // Buscar tiendas relacionadas si hay búsqueda por texto
    let stores: StoreCache[] = [];
    if (searchDto.q && !searchDto.storeId) {
      stores = await this.storeCacheModel
        .find({
          isActive: true,
          $or: [
            { storeName: { $regex: searchDto.q, $options: 'i' } },
            { description: { $regex: searchDto.q, $options: 'i' } },
          ],
        })
        .limit(5)
        .exec();
    }

    return {
      products,
      stores,
      total: totalProducts,
      page,
      totalPages: Math.ceil(totalProducts / limit),
    };
  }

  async searchByCategory(categoryId: string, filters: CategoryFiltersDto): Promise<{
    products: Product[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const query: any = {
      category: new Types.ObjectId(categoryId),
      isActive: true,
      isAvailable: true,
    };

    // Aplicar filtros
    if (filters.storeId) {
      query.storeId = filters.storeId;
    }

    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      query.price = {};
      if (filters.minPrice !== undefined) query.price.$gte = filters.minPrice;
      if (filters.maxPrice !== undefined) query.price.$lte = filters.maxPrice;
    }

    if (filters.tags && filters.tags.length > 0) {
      query.tags = { $in: filters.tags };
    }

    // Configurar ordenamiento
    const sort: any = {};
    if (filters.sortBy) {
      sort[filters.sortBy] = filters.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.rating = -1;
    }

    // Paginación
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .populate('category')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    return {
      products,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getSearchSuggestions(query: string): Promise<{
    products: string[];
    stores: string[];
    categories: string[];
  }> {
    const regex = new RegExp(query, 'i');

    // Sugerencias de productos
    const productSuggestions = await this.productModel
      .find({
        name: regex,
        isActive: true,
        isAvailable: true,
      })
      .select('name')
      .limit(5)
      .exec();

    // Sugerencias de tiendas
    const storeSuggestions = await this.storeCacheModel
      .find({
        storeName: regex,
        isActive: true,
      })
      .select('storeName')
      .limit(5)
      .exec();

    // Sugerencias de categorías (esto requeriría importar el modelo de Category)
    // Por ahora lo dejamos vacío
    const categorySuggestions: string[] = [];

    return {
      products: productSuggestions.map(p => p.name),
      stores: storeSuggestions.map(s => s.storeName),
      categories: categorySuggestions,
    };
  }

  async getPopularSearches(): Promise<string[]> {
    // Esto podría implementarse con un sistema de tracking de búsquedas
    // Por ahora retornamos algunas búsquedas populares predefinidas
    return [
      'pizza',
      'hamburguesa',
      'sushi',
      'empanadas',
      'completos',
      'pollo',
      'ensaladas',
      'postres',
    ];
  }

  async getTrendingProducts(): Promise<Product[]> {
    return this.productModel
      .find({
        isActive: true,
        isAvailable: true,
        rating: { $gte: 4 },
      })
      .populate('category')
      .sort({ reviewCount: -1, rating: -1 })
      .limit(10)
      .exec();
  }
}
