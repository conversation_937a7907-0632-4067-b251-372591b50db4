import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CatalogAuthGuard } from '../auth-client/guards/catalog-auth.guard';

@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @UseGuards(CatalogAuthGuard)
  async create(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoryService.create(createCategoryDto);
  }

  @Get()
  async findAll() {
    return this.categoryService.findAll();
  }

  @Get('main')
  async findMainCategories() {
    return this.categoryService.findMainCategories();
  }

  @Get('with-count')
  async findCategoriesWithProductCount() {
    return this.categoryService.findCategoriesWithProductCount();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.categoryService.findOne(id);
  }

  @Get(':id/subcategories')
  async findSubCategories(@Param('id') id: string) {
    return this.categoryService.findSubCategories(id);
  }

  @Patch(':id')
  @UseGuards(CatalogAuthGuard)
  async update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ) {
    return this.categoryService.update(id, updateCategoryDto);
  }

  @Delete(':id')
  @UseGuards(CatalogAuthGuard)
  async remove(@Param('id') id: string) {
    return this.categoryService.remove(id);
  }
}
