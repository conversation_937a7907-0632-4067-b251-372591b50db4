import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StoreService } from './store.service';
import { StoreSyncService } from './store-sync.service';
import { StoreController } from './store.controller';
import { StoreCache, StoreCacheSchema } from './store-cache.model';
import { AuthClientModule } from '../auth-client/auth-client.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: StoreCache.name, schema: StoreCacheSchema }]),
    AuthClientModule,
  ],
  controllers: [StoreController],
  providers: [StoreService, StoreSyncService],
  exports: [StoreService, StoreSyncService],
})
export class StoreModule {}
