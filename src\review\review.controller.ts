import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  Patch,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ReviewService } from './review.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { CatalogAuthGuard } from '../auth-client/guards/catalog-auth.guard';
import { GetUser } from '../auth-client/decorators/get-user.decorator';
import { AuthUser } from '../auth-client/auth-client.service';

@Controller('reviews')
export class ReviewController {
  constructor(private readonly reviewService: ReviewService) {}

  @Post()
  @UseGuards(CatalogAuthGuard)
  async create(
    @Body() createReviewDto: CreateReviewDto,
    @GetUser() user: AuthUser,
  ) {
    return this.reviewService.create(createReviewDto, user.id, user.name);
  }

  @Get('product/:productId')
  async findByProduct(
    @Param('productId') productId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.reviewService.findByProduct(productId, page, limit);
  }

  @Get('user/:userId')
  async findByUser(
    @Param('userId') userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.reviewService.findByUser(userId, page, limit);
  }

  @Get('my-reviews')
  @UseGuards(CatalogAuthGuard)
  async findMyReviews(
    @GetUser() user: AuthUser,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.reviewService.findByUser(user.id, page, limit);
  }

  @Post(':id/images')
  @UseGuards(CatalogAuthGuard)
  @UseInterceptors(FilesInterceptor('images', 3))
  async uploadImages(
    @Param('id') id: string,
    @UploadedFiles() files: Express.Multer.File[],
    @GetUser() user: AuthUser,
  ) {
    return this.reviewService.uploadReviewImages(id, files, user.id);
  }

  @Patch(':id/helpful')
  async markHelpful(@Param('id') id: string) {
    return this.reviewService.markHelpful(id);
  }

  @Delete(':id')
  @UseGuards(CatalogAuthGuard)
  async remove(
    @Param('id') id: string,
    @GetUser() user: AuthUser,
  ) {
    return this.reviewService.remove(id, user.id);
  }
}
