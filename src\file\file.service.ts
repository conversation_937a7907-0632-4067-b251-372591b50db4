import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sharp from 'sharp';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FileService {
  private readonly uploadPath: string;
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    this.uploadPath = this.configService.get<string>('UPLOAD_PATH') || './uploads';
    this.baseUrl = this.configService.get<string>('API_BASE_URL') || 'http://localhost:3002';
    
    // Crear directorio de uploads si no existe
    this.ensureUploadDirectories();
  }

  private ensureUploadDirectories() {
    const directories = [
      this.uploadPath,
      path.join(this.uploadPath, 'products'),
      path.join(this.uploadPath, 'stores'),
      path.join(this.uploadPath, 'reviews'),
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  async uploadProductImages(files: Express.Multer.File[]): Promise<string[]> {
    const imageUrls: string[] = [];

    for (const file of files) {
      const filename = `${uuidv4()}.webp`;
      const filepath = path.join(this.uploadPath, 'products', filename);

      // Procesar imagen con Sharp
      await sharp(file.buffer)
        .resize(800, 600, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .webp({ quality: 80 })
        .toFile(filepath);

      const imageUrl = `${this.baseUrl}/api/files/products/${filename}`;
      imageUrls.push(imageUrl);
    }

    return imageUrls;
  }

  async uploadStoreImage(file: Express.Multer.File, type: 'logo' | 'cover'): Promise<string> {
    const filename = `${uuidv4()}.webp`;
    const filepath = path.join(this.uploadPath, 'stores', filename);

    const size = type === 'logo' ? { width: 200, height: 200 } : { width: 1200, height: 400 };

    await sharp(file.buffer)
      .resize(size.width, size.height, {
        fit: type === 'logo' ? 'cover' : 'inside',
        withoutEnlargement: true,
      })
      .webp({ quality: 80 })
      .toFile(filepath);

    return `${this.baseUrl}/api/files/stores/${filename}`;
  }

  async uploadReviewImages(files: Express.Multer.File[]): Promise<string[]> {
    const imageUrls: string[] = [];

    for (const file of files) {
      const filename = `${uuidv4()}.webp`;
      const filepath = path.join(this.uploadPath, 'reviews', filename);

      await sharp(file.buffer)
        .resize(600, 600, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .webp({ quality: 75 })
        .toFile(filepath);

      const imageUrl = `${this.baseUrl}/api/files/reviews/${filename}`;
      imageUrls.push(imageUrl);
    }

    return imageUrls;
  }

  async deleteFile(fileUrl: string): Promise<void> {
    try {
      // Extraer el path relativo de la URL
      const urlParts = fileUrl.split('/api/files/');
      if (urlParts.length === 2) {
        const relativePath = urlParts[1];
        const fullPath = path.join(this.uploadPath, relativePath);
        
        if (fs.existsSync(fullPath)) {
          fs.unlinkSync(fullPath);
        }
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  }

  getFilePath(category: string, filename: string): string {
    return path.join(this.uploadPath, category, filename);
  }
}
