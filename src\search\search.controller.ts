import { Controller, Get, Query, Param } from '@nestjs/common';
import { SearchService } from './search.service';
import { SearchDto, CategoryFiltersDto } from './dto/search.dto';

@Controller('search')
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Get()
  async globalSearch(@Query() searchDto: SearchDto) {
    return this.searchService.globalSearch(searchDto);
  }

  @Get('category/:categoryId')
  async searchByCategory(
    @Param('categoryId') categoryId: string,
    @Query() filters: CategoryFiltersDto,
  ) {
    return this.searchService.searchByCategory(categoryId, filters);
  }

  @Get('suggestions')
  async getSearchSuggestions(@Query('q') query: string) {
    return this.searchService.getSearchSuggestions(query);
  }

  @Get('popular')
  async getPopularSearches() {
    return this.searchService.getPopularSearches();
  }

  @Get('trending')
  async getTrendingProducts() {
    return this.searchService.getTrendingProducts();
  }
}
