import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { StoreCache, StoreCacheDocument } from './store-cache.model';
import { StoreFiltersDto } from './dto/store-filters.dto';
import { StoreInfo } from '../auth-client/auth-client.service';

@Injectable()
export class StoreService {
  constructor(
    @InjectModel(StoreCache.name) private storeCacheModel: Model<StoreCacheDocument>,
  ) {}

  async findAll(filters: StoreFiltersDto): Promise<{ stores: StoreCache[]; total: number; page: number; totalPages: number }> {
    const query: any = { isActive: true };

    // Aplicar filtros
    if (filters.search) {
      query.$or = [
        { storeName: { $regex: filters.search, $options: 'i' } },
        { description: { $regex: filters.search, $options: 'i' } },
        { storeAddress: { $regex: filters.search, $options: 'i' } },
      ];
    }

    if (filters.isOpen !== undefined) {
      query.isOpen = filters.isOpen;
    }

    if (filters.minRating !== undefined) {
      query.rating = { $gte: filters.minRating };
    }

    if (filters.maxDeliveryFee !== undefined) {
      query.deliveryFee = { $lte: filters.maxDeliveryFee };
    }

    // Configurar ordenamiento
    const sort: any = {};
    if (filters.sortBy) {
      sort[filters.sortBy] = filters.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.rating = -1;
    }

    // Paginación
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    const [stores, total] = await Promise.all([
      this.storeCacheModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.storeCacheModel.countDocuments(query),
    ]);

    return {
      stores,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(storeId: string): Promise<StoreCache> {
    const store = await this.storeCacheModel.findOne({ storeId }).exec();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    return store;
  }

  async updateStoreCache(storeInfo: StoreInfo): Promise<StoreCache> {
    const existingStore = await this.storeCacheModel.findOne({ storeId: storeInfo.id });

    if (existingStore) {
      // Actualizar tienda existente
      existingStore.storeName = storeInfo.storeName;
      existingStore.storeAddress = storeInfo.storeAddress;
      existingStore.storePhone = storeInfo.storePhone;
      existingStore.email = storeInfo.email;
      existingStore.lastSync = new Date();
      
      return existingStore.save();
    } else {
      // Crear nueva entrada en cache
      const newStore = new this.storeCacheModel({
        storeId: storeInfo.id,
        storeName: storeInfo.storeName,
        storeAddress: storeInfo.storeAddress,
        storePhone: storeInfo.storePhone,
        email: storeInfo.email,
        lastSync: new Date(),
      });

      return newStore.save();
    }
  }

  async getStoreCategories(storeId: string): Promise<any[]> {
    // Obtener categorías de productos de una tienda específica
    return this.storeCacheModel.aggregate([
      { $match: { storeId } },
      {
        $lookup: {
          from: 'products',
          localField: 'storeId',
          foreignField: 'storeId',
          as: 'products'
        }
      },
      { $unwind: '$products' },
      {
        $lookup: {
          from: 'categories',
          localField: 'products.category',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: '$category' },
      {
        $group: {
          _id: '$category._id',
          name: { $first: '$category.name' },
          description: { $first: '$category.description' },
          icon: { $first: '$category.icon' },
          productCount: { $sum: 1 }
        }
      },
      { $sort: { name: 1 } }
    ]);
  }

  async updateStoreRating(storeId: string, newRating: number, reviewCount: number): Promise<void> {
    await this.storeCacheModel.updateOne(
      { storeId },
      { 
        $set: { 
          rating: newRating,
          reviewCount: reviewCount 
        } 
      }
    );
  }

  async toggleStoreStatus(storeId: string, isOpen: boolean): Promise<StoreCache> {
    const store = await this.storeCacheModel.findOneAndUpdate(
      { storeId },
      { isOpen },
      { new: true }
    );

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    return store;
  }
}
