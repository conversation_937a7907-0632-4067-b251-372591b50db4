import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  Patch,
  Body,
} from '@nestjs/common';
import { StoreService } from './store.service';
import { StoreSyncService } from './store-sync.service';
import { StoreFiltersDto } from './dto/store-filters.dto';
import { CatalogAuthGuard } from '../auth-client/guards/catalog-auth.guard';
import { StoreOwnerGuard } from '../auth-client/guards/store-owner.guard';
import { GetUser } from '../auth-client/decorators/get-user.decorator';
import { AuthUser } from '../auth-client/auth-client.service';

@Controller('stores')
export class StoreController {
  constructor(
    private readonly storeService: StoreService,
    private readonly storeSyncService: StoreSyncService,
  ) {}

  @Get()
  async findAll(@Query() filters: StoreFiltersDto) {
    return this.storeService.findAll(filters);
  }

  @Get(':storeId')
  async findOne(@Param('storeId') storeId: string) {
    return this.storeService.findOne(storeId);
  }

  @Get(':storeId/categories')
  async getStoreCategories(@Param('storeId') storeId: string) {
    return this.storeService.getStoreCategories(storeId);
  }

  @Post('sync')
  @UseGuards(CatalogAuthGuard)
  async forceSyncAllStores() {
    await this.storeSyncService.forceSyncAllStores();
    return { message: 'Store synchronization initiated' };
  }

  @Post(':storeId/sync')
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  async syncSingleStore(@Param('storeId') storeId: string) {
    await this.storeSyncService.syncSingleStore(storeId);
    return { message: 'Store synchronized successfully' };
  }

  @Patch(':storeId/status')
  @UseGuards(CatalogAuthGuard, StoreOwnerGuard)
  async toggleStoreStatus(
    @Param('storeId') storeId: string,
    @Body('isOpen') isOpen: boolean,
    @GetUser() user: AuthUser,
  ) {
    return this.storeService.toggleStoreStatus(storeId, isOpen);
  }
}
