# Microservicio de Catálogo

Microservicio para gestión de productos, categorías, tiendas y búsquedas, desarrollado con NestJS y MongoDB.

## 🚀 Características

- **Gestión de Productos**: CRUD completo con imágenes, categorías y personalizaciones
- **Sistema de Categorías**: Categorías principales y subcategorías
- **Cache de Tiendas**: Sincronización automática con microservicio de autenticación
- **Búsqueda Avanzada**: Búsqueda por texto, filtros y sugerencias
- **Sistema de Reseñas**: Calificaciones y comentarios de productos
- **Subida de Imágenes**: Procesamiento automático con Sharp
- **Comunicación TCP**: Integración con microservicio de autenticación
- **Autenticación JWT**: Validación de tokens a través de TCP

## 🛠️ Tecnologías

- **NestJS**: Framework principal
- **MongoDB**: Base de datos con Mongoose
- **TCP Client**: Comunicación entre microservicios
- **Sharp**: Procesamiento de imágenes
- **Multer**: Subida de archivos
- **JWT**: Autenticación
- **Cron Jobs**: Sincronización automática

## 📋 Requisitos Previos

- Node.js 18+
- MongoDB 5.0+
- Microservicio de autenticación corriendo en puerto 3001

## 🔧 Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd microservicio-catalogo
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
```

Editar `.env` con tus configuraciones:
```env
# Base de datos
MONGODB_URI=mongodb://localhost:27017/CatalogDB

# Servidor
PORT=3002

# Microservicio de autenticación
AUTH_SERVICE_HOST=localhost
AUTH_SERVICE_PORT=3001

# Archivos
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# URLs
FRONTEND_URL=http://localhost:3000
API_BASE_URL=http://localhost:3002
```

4. **Crear directorios de uploads**
```bash
mkdir -p uploads/products uploads/stores uploads/reviews
```

5. **Iniciar el servidor**
```bash
# Desarrollo
npm run start:dev

# Producción
npm run build
npm run start:prod
```

## 📡 Endpoints Principales

### Productos
- `GET /api/products` - Listar productos con filtros
- `GET /api/products/store/:storeId` - Productos por tienda
- `GET /api/products/:id` - Detalle de producto
- `POST /api/products` - Crear producto (requiere auth)
- `PUT /api/products/:id` - Actualizar producto (requiere auth)
- `DELETE /api/products/:id` - Eliminar producto (requiere auth)
- `POST /api/products/:id/images` - Subir imágenes (requiere auth)

### Categorías
- `GET /api/categories` - Listar categorías
- `GET /api/categories/main` - Categorías principales
- `GET /api/categories/:id/subcategories` - Subcategorías
- `POST /api/categories` - Crear categoría (requiere auth)

### Tiendas
- `GET /api/stores` - Listar tiendas con filtros
- `GET /api/stores/:storeId` - Información de tienda
- `GET /api/stores/:storeId/categories` - Categorías de una tienda
- `POST /api/stores/sync` - Sincronizar todas las tiendas (requiere auth)

### Búsqueda
- `GET /api/search?q=query` - Búsqueda global
- `GET /api/search/category/:categoryId` - Búsqueda por categoría
- `GET /api/search/suggestions?q=query` - Sugerencias de búsqueda
- `GET /api/search/trending` - Productos en tendencia

### Reseñas
- `GET /api/reviews/product/:productId` - Reseñas de producto
- `POST /api/reviews` - Crear reseña (requiere auth)
- `GET /api/reviews/my-reviews` - Mis reseñas (requiere auth)
- `POST /api/reviews/:id/images` - Subir imágenes de reseña (requiere auth)

### Archivos
- `GET /api/files/products/:filename` - Imagen de producto
- `GET /api/files/stores/:filename` - Imagen de tienda
- `GET /api/files/reviews/:filename` - Imagen de reseña

## 🔐 Autenticación

El microservicio utiliza JWT tokens validados a través del microservicio de autenticación:

```bash
# Incluir token en headers
Authorization: Bearer <jwt-token>
```

## 🏗️ Arquitectura

```
src/
├── auth-client/          # Comunicación TCP con auth
├── product/              # Gestión de productos
├── category/             # Gestión de categorías
├── store/                # Cache y sync de tiendas
├── search/               # Búsqueda y filtros
├── review/               # Sistema de reseñas
├── file/                 # Gestión de archivos
└── main.ts              # Punto de entrada
```

## 🔄 Sincronización de Tiendas

El microservicio sincroniza automáticamente la información de tiendas cada 6 horas:

- **Automática**: Cron job cada 6 horas
- **Manual**: `POST /api/stores/sync`
- **Individual**: `POST /api/stores/:storeId/sync`

## 📊 Base de Datos

### Modelos Principales:
- **Product**: Productos con categorías, imágenes y personalizaciones
- **Category**: Categorías jerárquicas
- **StoreCache**: Cache local de información de tiendas
- **Review**: Reseñas y calificaciones

### Índices Optimizados:
- Búsqueda por texto en productos
- Filtros por tienda, categoría y precio
- Geolocalización de tiendas

## 🧪 Testing

```bash
# Tests unitarios
npm run test

# Tests e2e
npm run test:e2e

# Coverage
npm run test:cov
```

## 📝 Logs

El microservicio registra:
- Sincronización de tiendas
- Errores de comunicación TCP
- Subida de archivos
- Operaciones de base de datos

## 🚀 Despliegue

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3002
CMD ["node", "dist/main"]
```

### Variables de Entorno Producción
```env
NODE_ENV=production
MONGODB_URI=mongodb://mongo:27017/CatalogDB
AUTH_SERVICE_HOST=auth-service
REDIS_URL=redis://redis:6379
```

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/AmazingFeature`)
3. Commit cambios (`git commit -m 'Add AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
